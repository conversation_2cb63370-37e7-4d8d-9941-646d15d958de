import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'sk.krvavydobsinsky.app',
  appName: 'App',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    allowNavigation: [
      'https://anchor.fm',
      'https://d3t3ozftmdmh3i.cloudfront.net',
      'https://raw.githubusercontent.com',
      'https://*.anchor.fm',
      'https://*.cloudfront.net'
    ],
    cleartext: true
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 0,
      launchAutoHide: true,
      showSpinner: false,
    },
    StatusBar: {
      style: 'DARK',
      backgroundColor: '#2C1A0C'
    },
    Keyboard: {
      resize: 'body',
      style: 'dark',
      resizeOnFullScreen: true
    },
    Preferences: {
      group: 'KrvavyDobsinskyApp'
    }
  },
  ios: {
    scheme: 'App',
    contentInset: 'automatic',
    backgroundColor: '#2C1A0C'
  }
};

export default config;
