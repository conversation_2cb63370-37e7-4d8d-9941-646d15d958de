<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON>rva<PERSON><PERSON></title>

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#673AB7" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />

  <!-- Icons -->
  <link rel="apple-touch-icon" href="https://i.imgur.com/F9fKCIr.png" />
  <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;700&family=Inter:wght@400;500;600;700&family=Lora:ital,wght@0,400;0,700;1,400;1,700&family=Spectral:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Content Security Policy - Allow external resources -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self' capacitor: https: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' capacitor: https:; style-src 'self' 'unsafe-inline' capacitor: https:; img-src 'self' data: blob: capacitor: https: http:; media-src 'self' data: blob: capacitor: https: http:; connect-src 'self' capacitor: https: http: ws: wss:; font-src 'self' data: capacitor: https:; object-src 'none'; base-uri 'self'; form-action 'self';">

</head>
<body class="font-sans antialiased" style="background-color: #F5F5DC; color: #2C1A0C;">
  <div class="min-h-screen flex flex-col" style="background-color: #F5F5DC; color: #2C1A0C;">
    <!-- Header -->
    <header class="py-3 px-4 md:px-6 sticky top-0 z-40 shadow-lg" style="background-color: #750000; color: #D2B48C;">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center gap-2 sm:gap-3">
          <a aria-label="Domov - Krvavý Dobšinský" class="flex items-center" href="/">
            <img alt="Domov - Krvavý Dobšinský" loading="lazy" width="32" height="32" decoding="async"
                 class="rounded-sm object-contain" src="https://i.imgur.com/F9fKCIr.png"/>
          </a>
          <h1 class="text-xl sm:text-2xl font-bold ml-1 sm:ml-0" style="font-family: 'Cormorant Garamond', serif; color: #D2B48C;">🔪 Krvavý Dobšinský</h1>
        </div>
        <div class="flex items-center">
          <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 w-10"
                  style="color: #D2B48C; background-color: rgba(210, 180, 140, 0.1);"
                  onmouseover="this.style.backgroundColor='rgba(210, 180, 140, 0.2)'"
                  onmouseout="this.style.backgroundColor='rgba(210, 180, 140, 0.1)'">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <span class="sr-only">Hľadať</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow transition-opacity duration-500 max-w-[375px] mx-auto w-full rounded-sm shadow-lg my-3 overflow-y-auto pb-16 opacity-0"
          id="main-content" style="background-color: #E6D7C3;">
      <div class="space-y-3 p-4 md:p-6" id="episodes-container">
        <!-- Episodes will be loaded here -->
        <div class="w-full p-4 md:p-5 bg-card shadow-lg rounded-xl">
          <div class="flex flex-col md:flex-row gap-4 sm:gap-5">
            <div class="w-full md:w-48 flex-shrink-0">
              <div class="animate-pulse aspect-square w-full rounded-lg bg-muted/50"></div>
            </div>
            <div class="flex-grow md:w-2/3 flex flex-col">
              <div class="animate-pulse rounded-md h-3 w-1/3 mb-2 bg-muted/50"></div>
              <div class="animate-pulse rounded-md h-7 w-3/4 mb-2 bg-muted/50"></div>
              <div class="animate-pulse h-11 w-full sm:max-w-xs rounded-xl bg-muted/50 mt-auto"></div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 px-4 py-2 z-50" style="background-color: #FAF0E6; border-top: 2px solid #D2B48C;">
      <div class="flex justify-around items-center max-w-md mx-auto">
        <a href="/" class="flex flex-col items-center py-2 px-3" style="color: #750000;">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">🔪 Podcast</span>
        </a>
        <a href="/read" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">📖 Čítať</span>
        </a>
        <a href="/saved" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">❤️ Uložené</span>
        </a>
        <a href="/bonus" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">⚡ Bonusy</span>
        </a>
      </div>
    </nav>
  </div>

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#750000',        // Krvavo červená
            'primary-foreground': '#D2B48C',  // Béžová
            background: '#F5F5DC',     // Béžová/krémová
            foreground: '#2C1A0C',     // Tmavo hnedá
            card: '#FAF0E6',          // Svetlá béžová
            'card-foreground': '#2C1A0C',
            muted: '#E6D7C3',         // Stredná béžová
            'muted-foreground': '#8B4513', // Hnedá
            accent: '#DAA520',        // Zlatá
            border: '#D2B48C',        // Béžová
            ring: '#750000'           // Krvavo červená
          },
          fontFamily: {
            'headline': ['Cormorant Garamond', 'serif'],
            'episodeTitle': ['Lora', 'serif'],
            'ui': ['Inter', 'sans-serif'],
            'sans': ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>

  <!-- Load episodes script -->
  <script>
    // Load RSS feed and display episodes
    async function loadEpisodes() {
      try {
        const response = await fetch('https://corsproxy.io/?https%3A%2F%2Fanchor.fm%2Fs%2F8db2e1ec%2Fpodcast%2Frss');
        const xmlText = await response.text();

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
        const items = xmlDoc.querySelectorAll('item');

        const container = document.getElementById('episodes-container');
        container.innerHTML = '';

        Array.from(items).slice(0, 10).forEach((item, index) => {
          const title = item.querySelector('title')?.textContent || 'Bez názvu';
          const description = item.querySelector('description')?.textContent || '';
          const audioUrl = item.querySelector('enclosure')?.getAttribute('url') || '';

          const episodeCard = `
            <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
                 style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
              <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
                <img alt="${title}" loading="lazy" decoding="async"
                     class="object-contain w-full h-full"
                     src="https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪"/>
              </div>
              <div class="p-4 md:p-5 flex flex-col flex-grow">
                <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                    style="font-family: 'Lora', serif; color: #2C1A0C;">${title}</h3>
                <div class="flex items-center gap-2 mt-auto">
                  <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                          style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                          onmouseover="this.style.backgroundColor='#8B0000'"
                          onmouseout="this.style.backgroundColor='#750000'"
                          onclick="playEpisode('${audioUrl}', '${title}')" aria-label="Prehrať epizódu">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                      <polygon points="6 3 20 12 6 21 6 3"></polygon>
                    </svg>
                    🔪 Prehrať epizódu
                  </button>
                </div>
              </div>
            </div>
          `;
          container.innerHTML += episodeCard;
        });

        // Show main content
        document.getElementById('main-content').style.opacity = '1';

      } catch (error) {
        console.error('Error loading episodes:', error);
        document.getElementById('episodes-container').innerHTML = '<p class="text-center p-4">Chyba pri načítavaní epizód</p>';
      }
    }

    function playEpisode(audioUrl, title) {
      alert(`Prehrávam: ${title}`);
      // Here you would implement actual audio playback
    }

    // Load episodes when page loads
    document.addEventListener('DOMContentLoaded', loadEpisodes);
  </script>

</body>
</html>
