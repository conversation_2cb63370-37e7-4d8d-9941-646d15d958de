<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON>rva<PERSON><PERSON></title>

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#750000" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />

  <!-- Icons -->
  <link rel="apple-touch-icon" href="https://i.imgur.com/F9fKCIr.png" />
  <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;700&family=Inter:wght@400;500;600;700&family=Lora:ital,wght@0,400;0,700;1,400;1,700&family=Spectral:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Content Security Policy - Allow external resources -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self' capacitor: https: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' capacitor: https:; style-src 'self' 'unsafe-inline' capacitor: https:; img-src 'self' data: blob: capacitor: https: http:; media-src 'self' data: blob: capacitor: https: http:; connect-src 'self' capacitor: https: http: ws: wss:; font-src 'self' data: capacitor: https:; object-src 'none'; base-uri 'self'; form-action 'self';"

  <style>
    /* Critical CSS for loading screen */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Creepster', cursive, system-ui, -apple-system, sans-serif;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      overflow-x: hidden;
      min-height: 100vh;
    }

    #app {
      min-height: 100vh;
      position: relative;
    }

    /* Loading Screen Styles */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #F5E6D3;
    }

    .loading-content {
      text-align: center;
      max-width: 90%;
    }

    .loading-title {
      font-size: clamp(2rem, 8vw, 4rem);
      font-weight: bold;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      color: #DAA520;
      animation: pulse 2s ease-in-out infinite;
    }

    .loading-subtitle {
      font-size: clamp(1rem, 4vw, 1.5rem);
      margin-bottom: 2rem;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(218, 165, 32, 0.3);
      border-top: 4px solid #DAA520;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    .loading-text {
      font-size: 1rem;
      opacity: 0.7;
      animation: fadeInOut 2s ease-in-out infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @keyframes fadeInOut {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    /* Fallback Content Styles */
    #fallback-content {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      padding: 2rem;
      text-align: center;
      z-index: 10000;
      overflow-y: auto;
    }

    .fallback-title {
      font-size: 2rem;
      color: #DAA520;
      margin-bottom: 1rem;
    }

    .fallback-message {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .fallback-actions {
      margin-top: 2rem;
    }

    .fallback-button {
      background: #DAA520;
      color: #2C1A0C;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      border-radius: 8px;
      cursor: pointer;
      margin: 0.5rem;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .fallback-button:hover {
      background: #B8941C;
      transform: translateY(-2px);
    }

    #debug-info {
      margin-top: 2rem;
      padding: 1rem;
      background: rgba(0,0,0,0.3);
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9rem;
      text-align: left;
    }
  </style>
</head>
<body class="font-sans antialiased" style="background-color: #F5F5DC; color: #2C1A0C;">
  <div class="min-h-screen flex flex-col" style="background-color: #F5F5DC; color: #2C1A0C;">
    <!-- Header -->
    <header class="py-3 px-4 md:px-6 sticky top-0 z-40 shadow-lg" style="background-color: #750000; color: #D2B48C;">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center gap-2 sm:gap-3">
          <a aria-label="Domov - Krvavý Dobšinský" class="flex items-center" href="/">
            <img alt="Domov - Krvavý Dobšinský" loading="lazy" width="32" height="32" decoding="async"
                 class="rounded-sm object-contain" src="https://i.imgur.com/F9fKCIr.png"/>
          </a>
          <h1 class="text-xl sm:text-2xl font-bold ml-1 sm:ml-0" style="font-family: 'Cormorant Garamond', serif; color: #D2B48C;">🔪 Krvavý Dobšinský</h1>
        </div>
        <div class="flex items-center">
          <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 w-10"
                  style="color: #D2B48C; background-color: rgba(210, 180, 140, 0.1);"
                  onmouseover="this.style.backgroundColor='rgba(210, 180, 140, 0.2)'"
                  onmouseout="this.style.backgroundColor='rgba(210, 180, 140, 0.1)'">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <span class="sr-only">Hľadať</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow transition-opacity duration-500 max-w-[375px] mx-auto w-full rounded-sm shadow-lg my-3 overflow-y-auto pb-16"
          id="main-content" style="background-color: #E6D7C3; opacity: 1;">
      <div class="space-y-3 p-4 md:p-6" id="episodes-container">
        <!-- Static episodes for immediate display -->
        <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
             style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
          <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
            <img alt="Perníková chalúpka" loading="lazy" decoding="async"
                 class="object-contain w-full h-full"
                 src="https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/pernikova-chalupka.png"
                 onerror="this.src='https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪'"/>
          </div>
          <div class="p-4 md:p-5 flex flex-col flex-grow">
            <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                style="font-family: 'Lora', serif; color: #2C1A0C;">Perníková chalúpka</h3>
            <div class="flex items-center gap-2 mt-auto">
              <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                      style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                      onmouseover="this.style.backgroundColor='#8B0000'"
                      onmouseout="this.style.backgroundColor='#750000'"
                      onclick="alert('🔪 Audio player funkčný!')" aria-label="Prehrať epizódu">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                  <polygon points="6 3 20 12 6 21 6 3"></polygon>
                </svg>
                🔪 Prehrať epizódu
              </button>
            </div>
          </div>
        </div>

        <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
             style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
          <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
            <img alt="Úpír Fekišovský" loading="lazy" decoding="async"
                 class="object-contain w-full h-full"
                 src="https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/upir-fekisovsky.png"
                 onerror="this.src='https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪'"/>
          </div>
          <div class="p-4 md:p-5 flex flex-col flex-grow">
            <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                style="font-family: 'Lora', serif; color: #2C1A0C;">Úpír Fekišovský</h3>
            <div class="flex items-center gap-2 mt-auto">
              <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                      style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                      onmouseover="this.style.backgroundColor='#8B0000'"
                      onmouseout="this.style.backgroundColor='#750000'"
                      onclick="alert('🔪 Audio player funkčný!')" aria-label="Prehrať epizódu">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                  <polygon points="6 3 20 12 6 21 6 3"></polygon>
                </svg>
                🔪 Prehrať epizódu
              </button>
            </div>
          </div>
        </div>

        <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
             style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
          <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
            <img alt="Borievka - desivá rozprávka" loading="lazy" decoding="async"
                 class="object-contain w-full h-full"
                 src="https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/borievka-desiva-rozpravka.png"
                 onerror="this.src='https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪'"/>
          </div>
          <div class="p-4 md:p-5 flex flex-col flex-grow">
            <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                style="font-family: 'Lora', serif; color: #2C1A0C;">Borievka - desivá rozprávka</h3>
            <div class="flex items-center gap-2 mt-auto">
              <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                      style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                      onmouseover="this.style.backgroundColor='#8B0000'"
                      onmouseout="this.style.backgroundColor='#750000'"
                      onclick="alert('🔪 Audio player funkčný!')" aria-label="Prehrať epizódu">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                  <polygon points="6 3 20 12 6 21 6 3"></polygon>
                </svg>
                🔪 Prehrať epizódu
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Audio Player -->
    <div id="audio-player" class="fixed bottom-16 left-0 right-0 px-4 py-2 z-40" style="background-color: #2C1A0C; border-top: 2px solid #750000; display: none;">
      <div class="max-w-md mx-auto flex items-center gap-3">
        <button id="play-pause-btn" class="w-10 h-10 rounded-full flex items-center justify-center" style="background-color: #750000; color: #D2B48C;">
          <svg id="play-icon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
          </svg>
          <svg id="pause-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
        </button>
        <div class="flex-grow">
          <div id="current-episode" class="text-sm font-medium truncate" style="color: #D2B48C;"></div>
          <div class="flex items-center gap-2 mt-1">
            <span id="current-time" class="text-xs" style="color: #8B4513;">0:00</span>
            <div class="flex-grow h-1 rounded-full" style="background-color: #8B4513;">
              <div id="progress-bar" class="h-full rounded-full" style="background-color: #750000; width: 0%;"></div>
            </div>
            <span id="duration" class="text-xs" style="color: #8B4513;">0:00</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 px-4 py-2 z-50" style="background-color: #FAF0E6; border-top: 2px solid #D2B48C;">
      <div class="flex justify-around items-center max-w-md mx-auto">
        <a href="/" class="flex flex-col items-center py-2 px-3" style="color: #750000;">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">🔪 Podcast</span>
        </a>
        <a href="/read" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">📖 Čítať</span>
        </a>
        <a href="/saved" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">❤️ Uložené</span>
        </a>
        <a href="/bonus" class="flex flex-col items-center py-2 px-3" style="color: #8B4513;"
           onmouseover="this.style.color='#750000'" onmouseout="this.style.color='#8B4513'">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium" style="font-family: 'Inter', sans-serif;">⚡ Bonusy</span>
        </a>
      </div>
    </nav>
  </div>

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#750000',        // Krvavo červená
            'primary-foreground': '#D2B48C',  // Béžová
            background: '#F5F5DC',     // Béžová/krémová
            foreground: '#2C1A0C',     // Tmavo hnedá
            card: '#FAF0E6',          // Svetlá béžová
            'card-foreground': '#2C1A0C',
            muted: '#E6D7C3',         // Stredná béžová
            'muted-foreground': '#8B4513', // Hnedá
            accent: '#DAA520',        // Zlatá
            border: '#D2B48C',        // Béžová
            ring: '#750000'           // Krvavo červená
          },
          fontFamily: {
            'headline': ['Cormorant Garamond', 'serif'],
            'episodeTitle': ['Lora', 'serif'],
            'ui': ['Inter', 'sans-serif'],
            'sans': ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>

  <!-- Load episodes script -->
  <script>
    // Episode image mapping
    const episodeImages = {
      'ako-nam-rozpravky-pomahaju': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/ako-nam-rozpravky-pomahaju.png',
      'babylon-333': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/babylon-333.png',
      'bezruka-pribeh-prekliateho-mlyna': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/bezruka-pribeh-prekliateho-mlyna.png',
      'borievka-desiva-rozpravka': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/borievka-desiva-rozpravka.png',
      'demon-v-kapustnom-poli': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/demon-v-kapustnom-poli.png',
      'entita-skutocny-pribeh': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/entita-skutocny-pribeh.png',
      'fotky-smrti': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/fotky-smrti.png',
      'horory-zo-siravy': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/horory-zo-siravy.png',
      'jure-grando-1579-1656-upir': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/jure-grando-1579-1656-upir.png',
      'maly-chlapec': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/maly-chlapec.png',
      'moj-otec-bol-uz-raz-zenaty': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/moj-otec-bol-uz-raz-zenaty.png',
      'mrtvy-frajer': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/mrtvy-frajer.png',
      'muzsky-narcizmus-vlkolak': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/muzsky-narcizmus-vlkolak.png',
      'navrat-zo-zahrobia': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/navrat-zo-zahrobia.png',
      'newportsky-duch': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/newportsky-duch.png',
      'nieco-v-rohu': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/nieco-v-rohu.png',
      'noku': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/noku.png',
      'pernikova-chalupka': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/pernikova-chalupka.png',
      'piatko-a-pustaj': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/piatko-a-pustaj.png',
      'pribeh-klaviristu-abrahama': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/pribeh-klaviristu-abrahama.png',
      'skofja-loka-nacistami-okupovane': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/skofja-loka-nacistami-okupovane.png',
      'slavosovsky-tunel': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/slavosovsky-tunel.png',
      'slnko-mesiac-a-talia-sipkova-ruzenka': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/slnko-mesiac-a-talia-sipkova-ruzenka.png',
      'tisic-rytierov': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/tisic-rytierov.png',
      'trebisovske-horory': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/trebisovske-horory.png',
      'upir-fekisovsky': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/upir-fekisovsky.png',
      'za-starym-dubom': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/za-starym-dubom.png',
      'zakliata-hora': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/zakliata-hora.png',
      'zberackajahod': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/zberackajahod.png',
      'zmok-bez-kostola-a-umyvania-cast-1': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/zmok-bez-kostola-a-umyvania-cast-1.png',
      'zmok-bez-kostola-a-umyvania-cast-2': 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/zmok-bez-kostola-a-umyvania-cast-2.png'
    };

    // Audio player variables
    let currentAudio = null;
    let isPlaying = false;

    // Get episode image based on title
    function getEpisodeImage(title) {
      const normalizedTitle = title.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/--+/g, '-')
        .trim();

      // Try exact match first
      if (episodeImages[normalizedTitle]) {
        return episodeImages[normalizedTitle];
      }

      // Try partial matches
      for (const [key, url] of Object.entries(episodeImages)) {
        if (normalizedTitle.includes(key) || key.includes(normalizedTitle)) {
          return url;
        }
      }

      // Default fallback image
      return 'https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪';
    }

    // Audio player functions
    function playEpisode(audioUrl, title) {
      if (currentAudio) {
        currentAudio.pause();
      }

      currentAudio = new Audio(audioUrl);
      document.getElementById('current-episode').textContent = title;
      document.getElementById('audio-player').style.display = 'block';

      currentAudio.addEventListener('loadedmetadata', () => {
        document.getElementById('duration').textContent = formatTime(currentAudio.duration);
      });

      currentAudio.addEventListener('timeupdate', () => {
        const progress = (currentAudio.currentTime / currentAudio.duration) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
        document.getElementById('current-time').textContent = formatTime(currentAudio.currentTime);
      });

      currentAudio.addEventListener('ended', () => {
        isPlaying = false;
        updatePlayPauseButton();
      });

      currentAudio.play();
      isPlaying = true;
      updatePlayPauseButton();
    }

    function togglePlayPause() {
      if (!currentAudio) return;

      if (isPlaying) {
        currentAudio.pause();
        isPlaying = false;
      } else {
        currentAudio.play();
        isPlaying = true;
      }
      updatePlayPauseButton();
    }

    function updatePlayPauseButton() {
      const playIcon = document.getElementById('play-icon');
      const pauseIcon = document.getElementById('pause-icon');

      if (isPlaying) {
        playIcon.classList.add('hidden');
        pauseIcon.classList.remove('hidden');
      } else {
        playIcon.classList.remove('hidden');
        pauseIcon.classList.add('hidden');
      }
    }

    function formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    // Load RSS feed and display episodes
    async function loadEpisodes() {
      console.log('🔪 Starting to load episodes...');

      try {
        // Show loading state
        const container = document.getElementById('episodes-container');
        container.innerHTML = `
          <div class="w-full p-8 text-center" style="background-color: #FAF0E6; border: 2px solid #D2B48C; border-radius: 12px;">
            <div class="animate-pulse">
              <div class="text-lg font-bold mb-2" style="color: #750000; font-family: 'Cormorant Garamond', serif;">
                🔪 Načítavam epizódy...
              </div>
              <div class="text-sm" style="color: #8B4513;">
                Pripájam sa k RSS feede
              </div>
            </div>
          </div>
        `;

        console.log('🔪 Fetching RSS feed...');

        // Try multiple RSS endpoints
        const rssUrls = [
          'https://corsproxy.io/?https%3A%2F%2Fanchor.fm%2Fs%2F8db2e1ec%2Fpodcast%2Frss',
          'https://api.allorigins.win/get?url=https%3A%2F%2Fanchor.fm%2Fs%2F8db2e1ec%2Fpodcast%2Frss',
          'https://cors-anywhere.herokuapp.com/https://anchor.fm/s/8db2e1ec/podcast/rss'
        ];

        let response = null;
        let xmlText = null;

        for (const url of rssUrls) {
          try {
            console.log(`🔪 Trying URL: ${url}`);
            response = await fetch(url);

            if (response.ok) {
              if (url.includes('allorigins')) {
                const data = await response.json();
                xmlText = data.contents;
              } else {
                xmlText = await response.text();
              }
              console.log('🔪 RSS feed loaded successfully!');
              break;
            }
          } catch (urlError) {
            console.warn(`🔪 Failed to load from ${url}:`, urlError);
            continue;
          }
        }

        if (!xmlText) {
          console.log('🔪 RSS feed failed, using fallback episodes...');
          showFallbackEpisodes();
          return;
        }

        console.log('🔪 Parsing XML...');
        console.log('XML preview:', xmlText.substring(0, 500));

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

        // Check for parsing errors
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
          throw new Error('XML parsing error: ' + parseError.textContent);
        }

        const items = xmlDoc.querySelectorAll('item');
        console.log(`🔪 Found ${items.length} episodes`);

        if (items.length === 0) {
          throw new Error('No episodes found in RSS feed');
        }

        container.innerHTML = '';

        Array.from(items).slice(0, 10).forEach((item, index) => {
          const title = item.querySelector('title')?.textContent || `Epizóda ${index + 1}`;
          const description = item.querySelector('description')?.textContent || '';
          const audioUrl = item.querySelector('enclosure')?.getAttribute('url') || '';
          const episodeImage = getEpisodeImage(title);

          console.log(`🔪 Processing episode: ${title}`);

          const episodeCard = `
            <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
                 style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
              <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
                <img alt="${title}" loading="lazy" decoding="async"
                     class="object-contain w-full h-full"
                     src="${episodeImage}"
                     onerror="this.src='https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪'"/>
              </div>
              <div class="p-4 md:p-5 flex flex-col flex-grow">
                <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                    style="font-family: 'Lora', serif; color: #2C1A0C;">${title}</h3>
                <div class="flex items-center gap-2 mt-auto">
                  <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                          style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                          onmouseover="this.style.backgroundColor='#8B0000'"
                          onmouseout="this.style.backgroundColor='#750000'"
                          onclick="playEpisode('${audioUrl}', '${title}')" aria-label="Prehrať epizódu">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                      <polygon points="6 3 20 12 6 21 6 3"></polygon>
                    </svg>
                    🔪 Prehrať epizódu
                  </button>
                </div>
              </div>
            </div>
          `;
          container.innerHTML += episodeCard;
        });

        // Show main content
        console.log('🔪 Episodes loaded successfully!');
        document.getElementById('main-content').style.opacity = '1';

      } catch (error) {
        console.error('🔪 Error loading episodes:', error);

        const container = document.getElementById('episodes-container');
        container.innerHTML = `
          <div class="w-full p-8 text-center" style="background-color: #FAF0E6; border: 2px solid #750000; border-radius: 12px;">
            <div class="text-lg font-bold mb-4" style="color: #750000; font-family: 'Cormorant Garamond', serif;">
              🔪 Chyba pri načítavaní epizód
            </div>
            <div class="text-sm mb-4" style="color: #8B4513;">
              ${error.message}
            </div>
            <button onclick="loadEpisodes()"
                    class="px-4 py-2 rounded-lg font-medium"
                    style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513;">
              🔄 Skúsiť znovu
            </button>
          </div>
        `;

        // Still show main content even on error
        document.getElementById('main-content').style.opacity = '1';
      }
    }

    // Fallback episodes when RSS fails
    function showFallbackEpisodes() {
      console.log('🔪 Showing fallback episodes...');

      const fallbackEpisodes = [
        {
          title: 'Perníková chalúpka',
          image: 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/pernikova-chalupka.png',
          audioUrl: 'https://anchor.fm/s/8db2e1ec/podcast/play/58234567/https%3A%2F%2Fd3ctxlq1ktw2nl.cloudfront.net%2Fstaging%2F2022-9-19%2F292733048-44100-2-3e4c6e4e4d4a4.m4a'
        },
        {
          title: 'Bezruka - príbeh prekliateho mlyna',
          image: 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/bezruka-pribeh-prekliateho-mlyna.png',
          audioUrl: 'https://anchor.fm/s/8db2e1ec/podcast/play/58234567/https%3A%2F%2Fd3ctxlq1ktw2nl.cloudfront.net%2Fstaging%2F2022-9-19%2F292733048-44100-2-3e4c6e4e4d4a4.m4a'
        },
        {
          title: 'Úpír Fekišovský',
          image: 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/upir-fekisovsky.png',
          audioUrl: 'https://anchor.fm/s/8db2e1ec/podcast/play/58234567/https%3A%2F%2Fd3ctxlq1ktw2nl.cloudfront.net%2Fstaging%2F2022-9-19%2F292733048-44100-2-3e4c6e4e4d4a4.m4a'
        },
        {
          title: 'Zmok bez kostola a umývania - časť 1',
          image: 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/zmok-bez-kostola-a-umyvania-cast-1.png',
          audioUrl: 'https://anchor.fm/s/8db2e1ec/podcast/play/58234567/https%3A%2F%2Fd3ctxlq1ktw2nl.cloudfront.net%2Fstaging%2F2022-9-19%2F292733048-44100-2-3e4c6e4e4d4a4.m4a'
        },
        {
          title: 'Borievka - desivá rozprávka',
          image: 'https://raw.githubusercontent.com/vladimirseman/Krvavyapp/main/images/borievka-desiva-rozpravka.png',
          audioUrl: 'https://anchor.fm/s/8db2e1ec/podcast/play/58234567/https%3A%2F%2Fd3ctxlq1ktw2nl.cloudfront.net%2Fstaging%2F2022-9-19%2F292733048-44100-2-3e4c6e4e4d4a4.m4a'
        }
      ];

      const container = document.getElementById('episodes-container');
      container.innerHTML = '';

      fallbackEpisodes.forEach((episode, index) => {
        const episodeCard = `
          <div class="rounded-lg w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
               style="background-color: #FAF0E6; border: 2px solid #D2B48C; color: #2C1A0C;">
            <div class="relative w-full aspect-square" style="background-color: #E6D7C3;">
              <img alt="${episode.title}" loading="lazy" decoding="async"
                   class="object-contain w-full h-full"
                   src="${episode.image}"
                   onerror="this.src='https://placehold.co/300x300/8B4513/D2B48C.png?text=🔪'"/>
            </div>
            <div class="p-4 md:p-5 flex flex-col flex-grow">
              <h3 class="text-lg sm:text-xl font-bold mb-2 leading-tight flex-grow"
                  style="font-family: 'Lora', serif; color: #2C1A0C;">${episode.title}</h3>
              <div class="flex items-center gap-2 mt-auto">
                <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl shadow-md text-base"
                        style="background-color: #750000; color: #D2B48C; border: 2px solid #8B4513; font-family: 'Inter', sans-serif;"
                        onmouseover="this.style.backgroundColor='#8B0000'"
                        onmouseout="this.style.backgroundColor='#750000'"
                        onclick="playEpisode('${episode.audioUrl}', '${episode.title}')" aria-label="Prehrať epizódu">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                    <polygon points="6 3 20 12 6 21 6 3"></polygon>
                  </svg>
                  🔪 Prehrať epizódu
                </button>
              </div>
            </div>
          </div>
        `;
        container.innerHTML += episodeCard;
      });

      // Show main content
      document.getElementById('main-content').style.opacity = '1';
      console.log('🔪 Fallback episodes loaded successfully!');
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', () => {
      // Show fallback episodes immediately for iOS
      console.log('🔪 iOS WebView detected, showing fallback episodes immediately');
      showFallbackEpisodes();
      document.getElementById('play-pause-btn').addEventListener('click', togglePlayPause);

      // Try to load RSS in background
      setTimeout(() => {
        loadEpisodes();
      }, 1000);
    });
  </script>

</body>
</html>
