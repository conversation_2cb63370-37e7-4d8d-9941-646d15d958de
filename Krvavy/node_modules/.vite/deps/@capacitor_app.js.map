{"version": 3, "sources": ["../../@capacitor/app/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { AppPlugin } from './definitions';\n\nconst App = registerPlugin<AppPlugin>('App', {\n  web: () => import('./web').then(m => new m.AppWeb()),\n});\n\nexport * from './definitions';\nexport { App };\n"], "mappings": ";;;;;;AAIA,IAAM,MAAM,eAA0B,OAAO;EAC3C,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,OAAM,CAAE;CACpD;", "names": []}