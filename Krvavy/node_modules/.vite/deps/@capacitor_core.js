import {
  Capacitor,
  CapacitorCookies,
  CapacitorException,
  CapacitorHttp,
  CapacitorPlatforms,
  ExceptionCode,
  Plugins,
  WebPlugin,
  WebView,
  addPlatform,
  buildRequestInit,
  registerPlugin,
  registerWebPlugin,
  setPlatform
} from "./chunk-RK7TOURC.js";
import "./chunk-PZ5AY32C.js";
export {
  Capacitor,
  CapacitorCookies,
  CapacitorException,
  CapacitorHttp,
  CapacitorPlatforms,
  ExceptionCode,
  Plugins,
  WebPlugin,
  WebView,
  addPlatform,
  buildRequestInit,
  registerPlugin,
  registerWebPlugin,
  setPlatform
};
//# sourceMappingURL=@capacitor_core.js.map
