{"hash": "cc6d34aa", "configHash": "4cd03075", "lockfileHash": "77621607", "browserHash": "839602fe", "optimized": {"vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "6bf30329", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "aa5194c9", "needsInterop": false}, "@capacitor/core": {"src": "../../@capacitor/core/dist/index.js", "file": "@capacitor_core.js", "fileHash": "0bbe5b29", "needsInterop": false}, "@capacitor/splash-screen": {"src": "../../@capacitor/splash-screen/dist/esm/index.js", "file": "@capacitor_splash-screen.js", "fileHash": "9c678497", "needsInterop": false}, "@capacitor/status-bar": {"src": "../../@capacitor/status-bar/dist/esm/index.js", "file": "@capacitor_status-bar.js", "fileHash": "7d5d04bb", "needsInterop": false}, "@capacitor/app": {"src": "../../@capacitor/app/dist/esm/index.js", "file": "@capacitor_app.js", "fileHash": "7ba002ca", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "4b3added", "needsInterop": false}, "@capacitor/preferences": {"src": "../../@capacitor/preferences/dist/esm/index.js", "file": "@capacitor_preferences.js", "fileHash": "cfb8dafb", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "566bcd5b", "needsInterop": false}, "@capacitor/share": {"src": "../../@capacitor/share/dist/esm/index.js", "file": "@capacitor_share.js", "fileHash": "19a8198f", "needsInterop": false}}, "chunks": {"web-L64TY2XR": {"file": "web-L64TY2XR.js"}, "web-SP4WBKMC": {"file": "web-SP4WBKMC.js"}, "web-SED24FAV": {"file": "web-SED24FAV.js"}, "web-7WUECABV": {"file": "web-7WUECABV.js"}, "chunk-RK7TOURC": {"file": "chunk-RK7TOURC.js"}, "chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-ZY5X6FX7": {"file": "chunk-ZY5X6FX7.js"}, "chunk-PZ5AY32C": {"file": "chunk-PZ5AY32C.js"}}}