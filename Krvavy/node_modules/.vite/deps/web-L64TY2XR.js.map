{"version": 3, "sources": ["../../@capacitor/share/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  CanShareResult,\n  ShareOptions,\n  SharePlugin,\n  ShareResult,\n} from './definitions';\n\nexport class ShareWeb extends WebPlugin implements SharePlugin {\n  async canShare(): Promise<CanShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      return { value: false };\n    } else {\n      return { value: true };\n    }\n  }\n  async share(options: ShareOptions): Promise<ShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      throw this.unavailable('Share API not available in this browser');\n    }\n\n    await navigator.share({\n      title: options.title,\n      text: options.text,\n      url: options.url,\n    });\n    return {};\n  }\n}\n"], "mappings": ";;;;;;AASM,IAAO,WAAP,cAAwB,UAAS;EACrC,MAAM,WAAQ;AACZ,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,OAAO;AACxD,aAAO,EAAE,OAAO,MAAK;WAChB;AACL,aAAO,EAAE,OAAO,KAAI;;EAExB;EACA,MAAM,MAAM,SAAqB;AAC/B,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,OAAO;AACxD,YAAM,KAAK,YAAY,yCAAyC;;AAGlE,UAAM,UAAU,MAAM;MACpB,OAAO,QAAQ;MACf,MAAM,QAAQ;MACd,KAAK,QAAQ;KACd;AACD,WAAO,CAAA;EACT;;", "names": []}