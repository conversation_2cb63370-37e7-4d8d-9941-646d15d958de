{"version": 3, "sources": ["../../@capacitor/app/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type { AppInfo, AppPlugin, AppLaunchUrl, AppState } from './definitions';\n\nexport class AppWeb extends WebPlugin implements AppPlugin {\n  constructor() {\n    super();\n    document.addEventListener(\n      'visibilitychange',\n      this.handleVisibilityChange,\n      false,\n    );\n  }\n\n  exitApp(): Promise<void> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async getInfo(): Promise<AppInfo> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async getLaunchUrl(): Promise<AppLaunchUrl> {\n    return { url: '' };\n  }\n\n  async getState(): Promise<AppState> {\n    return { isActive: document.hidden !== true };\n  }\n\n  async minimizeApp(): Promise<void> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  private handleVisibilityChange = () => {\n    const data = {\n      isActive: document.hidden !== true,\n    };\n\n    this.notifyListeners('appStateChange', data);\n    if (document.hidden) {\n      this.notifyListeners('pause', null);\n    } else {\n      this.notifyListeners('resume', null);\n    }\n  };\n}\n"], "mappings": ";;;;;;AAIM,IAAO,SAAP,cAAsB,UAAS;EACnC,cAAA;AACE,UAAK;AA4BC,SAAA,yBAAyB,MAAK;AACpC,YAAM,OAAO;QACX,UAAU,SAAS,WAAW;;AAGhC,WAAK,gBAAgB,kBAAkB,IAAI;AAC3C,UAAI,SAAS,QAAQ;AACnB,aAAK,gBAAgB,SAAS,IAAI;aAC7B;AACL,aAAK,gBAAgB,UAAU,IAAI;;IAEvC;AAtCE,aAAS,iBACP,oBACA,KAAK,wBACL,KAAK;EAET;EAEA,UAAO;AACL,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,UAAO;AACX,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,eAAY;AAChB,WAAO,EAAE,KAAK,GAAE;EAClB;EAEA,MAAM,WAAQ;AACZ,WAAO,EAAE,UAAU,SAAS,WAAW,KAAI;EAC7C;EAEA,MAAM,cAAW;AACf,UAAM,KAAK,cAAc,yBAAyB;EACpD;;", "names": []}