{"version": 3, "sources": ["../../@capacitor/splash-screen/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  HideOptions,\n  ShowOptions,\n  SplashScreenPlugin,\n} from './definitions';\n\nexport class SplashScreenWeb extends WebPlugin implements SplashScreenPlugin {\n  async show(_options?: ShowOptions): Promise<void> {\n    return undefined;\n  }\n\n  async hide(_options?: HideOptions): Promise<void> {\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;AAQM,IAAO,kBAAP,cAA+B,UAAS;EAC5C,MAAM,KAAK,UAAsB;AAC/B,WAAO;EACT;EAEA,MAAM,KAAK,UAAsB;AAC/B,WAAO;EACT;;", "names": []}