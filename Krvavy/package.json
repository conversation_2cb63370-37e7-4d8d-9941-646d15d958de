{"name": "krvavy-dobsinsky-app", "version": "1.0.0", "description": "Krvavý <PERSON>š<PERSON>ý - Horror Podcast iOS App", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "cap:add": "npx cap add ios", "cap:sync": "npx cap sync", "cap:open": "npx cap open ios", "cap:run": "npx cap run ios", "cap:build": "npm run build && npx cap sync && npx cap open ios"}, "dependencies": {"@capacitor/app": "^5.0.6", "@capacitor/core": "^5.5.1", "@capacitor/haptics": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@capacitor/splash-screen": "^5.0.6", "@capacitor/filesystem": "^5.1.4", "@capacitor/device": "^5.0.6", "@capacitor/network": "^5.0.6", "@capacitor/share": "^5.0.6", "@capacitor/browser": "^5.1.0", "@capacitor/preferences": "^5.0.6", "@capacitor/ios": "^5.5.1", "@capacitor-community/media": "^5.0.0", "vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "howler": "^2.2.4", "xml2js": "^0.6.2"}, "devDependencies": {"@capacitor/cli": "^5.5.1", "@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "typescript": "^5.2.2", "@types/node": "^20.8.10"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "keywords": ["podcast", "horror", "slovak", "ios", "capacitor", "mobile"]}